'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { 
  Calculator, 
  FileText, 
  Target, 
  TrendingUp, 
  Users, 
  BookOpen, 
  Clock, 
  Award,
  Settings2,
  ArrowRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Tool {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  category: 'assessment' | 'planning' | 'tracking' | 'resources';
  isAvailable: boolean;
  requiresAuth: boolean;
}

const tools: Tool[] = [
  {
    id: 'career-assessment',
    name: 'Career Assessment',
    description: 'Discover your strengths, interests, and ideal career paths',
    icon: <Target className="h-6 w-6" />,
    href: '/assessment',
    category: 'assessment',
    isAvailable: true,
    requiresAuth: true
  },
  {
    id: 'salary-calculator',
    name: 'Salary Calculator',
    description: 'Calculate expected salaries for different career paths',
    icon: <Calculator className="h-6 w-6" />,
    href: '/tools/salary-calculator',
    category: 'planning',
    isAvailable: true,
    requiresAuth: false
  },
  {
    id: 'resume-builder',
    name: 'Resume Builder',
    description: 'Create professional resumes tailored to your target roles',
    icon: <FileText className="h-6 w-6" />,
    href: '/tools/resume-builder',
    category: 'resources',
    isAvailable: false,
    requiresAuth: true
  },
  {
    id: 'progress-tracker',
    name: 'Progress Tracker',
    description: 'Track your learning progress and career milestones',
    icon: <TrendingUp className="h-6 w-6" />,
    href: '/dashboard',
    category: 'tracking',
    isAvailable: true,
    requiresAuth: true
  },
  {
    id: 'networking-planner',
    name: 'Networking Planner',
    description: 'Plan and track your professional networking activities',
    icon: <Users className="h-6 w-6" />,
    href: '/tools/networking-planner',
    category: 'planning',
    isAvailable: false,
    requiresAuth: true
  },
  {
    id: 'skill-gap-analyzer',
    name: 'Skill Gap Analyzer',
    description: 'Identify skills you need to develop for your target role',
    icon: <BookOpen className="h-6 w-6" />,
    href: '/tools/skill-gap-analyzer',
    category: 'assessment',
    isAvailable: false,
    requiresAuth: true
  },
  {
    id: 'interview-prep',
    name: 'Interview Prep',
    description: 'Practice common interview questions and scenarios',
    icon: <Award className="h-6 w-6" />,
    href: '/interview-practice',
    category: 'resources',
    isAvailable: true,
    requiresAuth: true
  },
  {
    id: 'time-tracker',
    name: 'Learning Time Tracker',
    description: 'Track time spent on learning and skill development',
    icon: <Clock className="h-6 w-6" />,
    href: '/tools/time-tracker',
    category: 'tracking',
    isAvailable: false,
    requiresAuth: true
  }
];

const categoryNames = {
  assessment: 'Assessment Tools',
  planning: 'Planning Tools',
  tracking: 'Tracking Tools',
  resources: 'Resource Tools'
};

const categoryColors = {
  assessment: 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800',
  planning: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
  tracking: 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800',
  resources: 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800'
};

export default function ToolsPage() {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  const groupedTools = tools.reduce((acc, tool) => {
    if (!acc[tool.category]) {
      acc[tool.category] = [];
    }
    acc[tool.category].push(tool);
    return acc;
  }, {} as Record<string, Tool[]>);

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Settings2 className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Career Tools</h1>
        </div>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Powerful tools to help you navigate your career transition journey
        </p>
      </div>

      {!isAuthenticated && (
        <div className="mb-8 p-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
            Sign in to access all tools
          </h3>
          <p className="text-yellow-700 dark:text-yellow-300 mb-4">
            Many of our career tools require an account to save your progress and provide personalized recommendations.
          </p>
          <Button asChild>
            <Link href="/api/auth/signin">Sign In</Link>
          </Button>
        </div>
      )}

      <div className="space-y-8">
        {Object.entries(groupedTools).map(([category, categoryTools]) => (
          <div key={category}>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              {categoryNames[category as keyof typeof categoryNames]}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoryTools.map((tool) => (
                <div
                  key={tool.id}
                  className={`p-6 border rounded-lg transition-all duration-200 hover:shadow-lg ${
                    categoryColors[tool.category]
                  } ${
                    !tool.isAvailable || (tool.requiresAuth && !isAuthenticated)
                      ? 'opacity-60'
                      : 'hover:scale-105'
                  }`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                        {tool.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {tool.name}
                        </h3>
                        {!tool.isAvailable && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            Coming Soon
                          </span>
                        )}
                      </div>
                    </div>
                    {tool.requiresAuth && (
                      <span className="text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded">
                        Account Required
                      </span>
                    )}
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                    {tool.description}
                  </p>
                  
                  {tool.isAvailable && (!tool.requiresAuth || isAuthenticated) ? (
                    <Link href={tool.href} className="w-full">
                      <Button className="w-full flex items-center justify-center gap-2">
                        Open Tool
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    </Link>
                  ) : (
                    <Button disabled className="w-full">
                      {!tool.isAvailable 
                        ? 'Coming Soon' 
                        : tool.requiresAuth && !isAuthenticated 
                        ? 'Sign In Required' 
                        : 'Open Tool'
                      }
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Need a specific tool?
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          We're constantly adding new tools to help with your career transition. 
          Let us know what tools would be most helpful for your journey.
        </p>
        <Button variant="outline" asChild>
          <Link href="/help">Request a Tool</Link>
        </Button>
      </div>
    </div>
  );
}
