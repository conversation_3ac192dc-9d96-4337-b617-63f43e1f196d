/**
 * Authentication Loading State Hook
 * Manages loading states for authentication processes
 */

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, usePathname } from 'next/navigation';

export type AuthLoadingState = 
  | 'idle'
  | 'checking'
  | 'signing-in'
  | 'signing-out'
  | 'redirecting'
  | 'verifying'
  | 'renewing'
  | 'error';

export interface AuthLoadingStateData {
  state: AuthLoadingState;
  message?: string;
  progress?: number;
  error?: string;
  isLoading: boolean;
  canRetry: boolean;
}

export interface UseAuthLoadingStateOptions {
  enableProgressTracking?: boolean;
  autoTransitions?: boolean;
  timeoutDuration?: number;
  onStateChange?: (state: AuthLoadingState) => void;
  onError?: (error: string) => void;
  onComplete?: () => void;
}

/**
 * Hook for managing authentication loading states
 */
export function useAuthLoadingState(options: UseAuthLoadingStateOptions = {}) {
  const {
    enableProgressTracking = false,
    autoTransitions = true,
    timeoutDuration = 30000, // 30 seconds
    onStateChange,
    onError,
    onComplete
  } = options;

  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  const [loadingState, setLoadingState] = useState<AuthLoadingStateData>({
    state: 'idle',
    isLoading: false,
    canRetry: false
  });

  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);

  // Update loading state
  const updateState = useCallback((
    newState: AuthLoadingState,
    message?: string,
    progress?: number,
    error?: string
  ) => {
    const stateData: AuthLoadingStateData = {
      state: newState,
      message,
      progress,
      error,
      isLoading: newState !== 'idle' && newState !== 'error',
      canRetry: newState === 'error'
    };

    setLoadingState(stateData);
    onStateChange?.(newState);

    if (error) {
      onError?.(error);
    }

    if (newState === 'idle') {
      onComplete?.();
    }

    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }

    // Set timeout for loading states
    if (stateData.isLoading && timeoutDuration > 0) {
      const id = setTimeout(() => {
        updateState('error', 'Operation timed out', undefined, 'The authentication process took too long to complete.');
      }, timeoutDuration);
      setTimeoutId(id);
    }
  }, [onStateChange, onError, onComplete, timeoutId, timeoutDuration]);

  // Handle NextAuth session status changes
  useEffect(() => {
    if (!autoTransitions) return;

    switch (status) {
      case 'loading':
        updateState('checking', 'Verifying your session...');
        break;
      case 'authenticated':
        updateState('idle');
        break;
      case 'unauthenticated':
        updateState('idle');
        break;
    }
  }, [status, autoTransitions, updateState]);

  // Manual state control functions
  const startSignIn = useCallback((message?: string) => {
    updateState('signing-in', message || 'Signing you in...');
  }, [updateState]);

  const startSignOut = useCallback((message?: string) => {
    updateState('signing-out', message || 'Signing you out...');
  }, [updateState]);

  const startRedirect = useCallback((message?: string) => {
    updateState('redirecting', message || 'Redirecting...');
  }, [updateState]);

  const startVerification = useCallback((message?: string) => {
    updateState('verifying', message || 'Verifying your identity...');
  }, [updateState]);

  const startRenewal = useCallback((message?: string) => {
    updateState('renewing', message || 'Renewing your session...');
  }, [updateState]);

  const setError = useCallback((error: string, message?: string) => {
    updateState('error', message || 'An error occurred', undefined, error);
  }, [updateState]);

  const setProgress = useCallback((progress: number, message?: string) => {
    if (!enableProgressTracking) return;
    
    setLoadingState(prev => ({
      ...prev,
      progress,
      message: message || prev.message
    }));
  }, [enableProgressTracking]);

  const complete = useCallback((message?: string) => {
    updateState('idle', message);
  }, [updateState]);

  const retry = useCallback(() => {
    if (loadingState.canRetry) {
      updateState('checking', 'Retrying...');
    }
  }, [loadingState.canRetry, updateState]);

  const reset = useCallback(() => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    updateState('idle');
  }, [timeoutId, updateState]);

  // Progress tracking for multi-step processes
  const createProgressTracker = useCallback((steps: string[]) => {
    let currentStep = 0;
    
    return {
      nextStep: (message?: string) => {
        if (currentStep < steps.length) {
          const progress = ((currentStep + 1) / steps.length) * 100;
          const stepMessage = message || steps[currentStep];
          setProgress(progress, stepMessage);
          currentStep++;
        }
      },
      setStep: (stepIndex: number, message?: string) => {
        if (stepIndex >= 0 && stepIndex < steps.length) {
          currentStep = stepIndex;
          const progress = ((stepIndex + 1) / steps.length) * 100;
          const stepMessage = message || steps[stepIndex];
          setProgress(progress, stepMessage);
        }
      },
      complete: () => {
        setProgress(100, 'Complete');
        setTimeout(() => complete(), 500);
      }
    };
  }, [setProgress, complete]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  return {
    // State data
    ...loadingState,
    
    // Session info
    session,
    sessionStatus: status,
    
    // State control
    startSignIn,
    startSignOut,
    startRedirect,
    startVerification,
    startRenewal,
    setError,
    setProgress,
    complete,
    retry,
    reset,
    
    // Progress tracking
    createProgressTracker,
    
    // Utilities
    isAuthenticated: status === 'authenticated',
    isUnauthenticated: status === 'unauthenticated',
    isSessionLoading: status === 'loading'
  };
}

/**
 * Hook for authentication redirect handling
 */
export function useAuthRedirect(options: {
  redirectTo?: string;
  requireAuth?: boolean;
  onRedirect?: (url: string) => void;
} = {}) {
  const { redirectTo = '/dashboard', requireAuth = true, onRedirect } = options;
  const { isAuthenticated, isUnauthenticated, isSessionLoading } = useAuthLoadingState();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Don't redirect while session is loading
    if (isSessionLoading) return;

    // Redirect unauthenticated users to login
    if (requireAuth && isUnauthenticated && pathname !== '/login') {
      const loginUrl = `/login?callbackUrl=${encodeURIComponent(pathname)}`;
      onRedirect?.(loginUrl);
      router.push(loginUrl);
      return;
    }

    // Redirect authenticated users away from auth pages
    if (isAuthenticated && (pathname === '/login' || pathname === '/register')) {
      onRedirect?.(redirectTo);
      router.push(redirectTo);
      return;
    }
  }, [
    isAuthenticated,
    isUnauthenticated,
    isSessionLoading,
    pathname,
    redirectTo,
    requireAuth,
    router,
    onRedirect
  ]);

  return {
    isAuthenticated,
    isUnauthenticated,
    isSessionLoading,
    shouldRedirect: (requireAuth && isUnauthenticated) || (isAuthenticated && (pathname === '/login' || pathname === '/register'))
  };
}

/**
 * Hook for authentication form states
 */
export function useAuthFormState() {
  const authState = useAuthLoadingState({
    enableProgressTracking: true,
    autoTransitions: false
  });

  const handleFormSubmit = useCallback(async (
    submitFn: () => Promise<void>,
    steps?: string[]
  ) => {
    try {
      if (steps) {
        const tracker = authState.createProgressTracker(steps);
        authState.startSignIn('Preparing...');
        
        // Simulate progress through steps
        for (let i = 0; i < steps.length; i++) {
          tracker.nextStep();
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        await submitFn();
        tracker.complete();
      } else {
        authState.startSignIn();
        await submitFn();
        authState.complete('Sign in successful!');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      authState.setError(errorMessage);
    }
  }, [authState]);

  return {
    ...authState,
    handleFormSubmit
  };
}
