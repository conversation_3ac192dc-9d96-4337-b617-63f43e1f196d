'use client';

// Force dynamic rendering for error pages
export const dynamic = 'force-dynamic';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html lang="en">
      <head>
        <title>Error - Something went wrong</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <style dangerouslySetInnerHTML={{
          __html: `
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
              font-family: system-ui, -apple-system, sans-serif; 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh; 
              display: flex; 
              align-items: center; 
              justify-content: center; 
              color: #333;
            }
            .container { 
              max-width: 500px; 
              width: 90%; 
              background: white; 
              border-radius: 16px; 
              box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
              padding: 3rem 2rem; 
              text-align: center; 
            }
            .error-icon { 
              font-size: 4rem; 
              margin-bottom: 1.5rem; 
              color: #ef4444; 
            }
            .error-title { 
              font-size: 2rem; 
              font-weight: 700; 
              color: #1f2937; 
              margin-bottom: 1rem; 
            }
            .error-message { 
              color: #6b7280; 
              margin-bottom: 2rem; 
              line-height: 1.6;
            }
            .button-group { 
              display: flex; 
              gap: 1rem; 
              justify-content: center; 
              flex-wrap: wrap;
            }
            .btn { 
              padding: 0.75rem 1.5rem; 
              border-radius: 8px; 
              text-decoration: none; 
              font-weight: 600; 
              border: none;
              cursor: pointer;
              transition: all 0.2s;
            }
            .btn-primary { 
              background: #2563eb; 
              color: white; 
            }
            .btn-primary:hover { 
              background: #1d4ed8; 
            }
            .btn-secondary { 
              background: #f3f4f6; 
              color: #374151; 
            }
            .btn-secondary:hover { 
              background: #e5e7eb; 
            }
          `
        }} />
      </head>
      <body>
        <div className="container">
          <div className="error-icon">⚠️</div>
          <h1 className="error-title">Something went wrong</h1>
          <p className="error-message">
            An unexpected error occurred. We apologize for the inconvenience.
          </p>
          <div className="button-group">
            <button onClick={reset} className="btn btn-primary">
              Try Again
            </button>
            <a href="/" className="btn btn-secondary">
              Go Home
            </a>
          </div>
        </div>
      </body>
    </html>
  );
}
