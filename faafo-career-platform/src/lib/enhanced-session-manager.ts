/**
 * Enhanced Session Management System
 * Provides advanced session security, monitoring, and lifecycle management
 */

import { getServerSession } from 'next-auth/next';
import { getSession, signOut } from 'next-auth/react';
import { authOptions } from '@/lib/auth';
import { ErrorTracker } from '@/lib/errorTracking';

export interface SessionMetadata {
  sessionId: string;
  userId: string;
  createdAt: number;
  lastActivity: number;
  ipAddress?: string;
  userAgent?: string;
  deviceFingerprint?: string;
  isActive: boolean;
  securityLevel: 'low' | 'medium' | 'high';
  flags: string[];
}

export interface SessionSecurityEvent {
  type: 'login' | 'logout' | 'timeout' | 'suspicious_activity' | 'concurrent_session' | 'security_violation';
  sessionId: string;
  userId: string;
  timestamp: number;
  details: Record<string, any>;
  severity: 'info' | 'warning' | 'critical';
}

export interface SessionConfig {
  maxAge: number; // Maximum session age in milliseconds
  inactivityTimeout: number; // Inactivity timeout in milliseconds
  maxConcurrentSessions: number; // Maximum concurrent sessions per user
  securityChecks: boolean; // Enable security checks
  deviceTracking: boolean; // Enable device tracking
  ipValidation: boolean; // Enable IP validation
  suspiciousActivityDetection: boolean; // Enable suspicious activity detection
}

export class EnhancedSessionManager {
  private static errorTracker = new ErrorTracker();
  private static sessions = new Map<string, SessionMetadata>();
  private static securityEvents: SessionSecurityEvent[] = [];
  
  private static readonly DEFAULT_CONFIG: SessionConfig = {
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    inactivityTimeout: 24 * 60 * 60 * 1000, // 24 hours
    maxConcurrentSessions: 5,
    securityChecks: true,
    deviceTracking: true,
    ipValidation: true,
    suspiciousActivityDetection: true
  };

  /**
   * Initialize session with enhanced security
   */
  static async initializeSession(
    userId: string,
    request?: Request,
    config: Partial<SessionConfig> = {}
  ): Promise<SessionMetadata> {
    const sessionConfig = { ...this.DEFAULT_CONFIG, ...config };
    const sessionId = this.generateSecureSessionId();
    const now = Date.now();

    // Extract request metadata
    const ipAddress = this.extractIpAddress(request);
    const userAgent = request?.headers.get('user-agent') || undefined;
    const deviceFingerprint = await this.generateDeviceFingerprint(request);

    // Check for concurrent sessions
    if (sessionConfig.maxConcurrentSessions > 0) {
      await this.enforceConcurrentSessionLimit(userId, sessionConfig.maxConcurrentSessions);
    }

    // Create session metadata
    const sessionMetadata: SessionMetadata = {
      sessionId,
      userId,
      createdAt: now,
      lastActivity: now,
      ipAddress,
      userAgent,
      deviceFingerprint,
      isActive: true,
      securityLevel: this.calculateSecurityLevel(request),
      flags: []
    };

    // Store session
    this.sessions.set(sessionId, sessionMetadata);

    // Log security event
    await this.logSecurityEvent({
      type: 'login',
      sessionId,
      userId,
      timestamp: now,
      details: {
        ipAddress,
        userAgent,
        deviceFingerprint,
        securityLevel: sessionMetadata.securityLevel
      },
      severity: 'info'
    });

    // Store in persistent storage if needed
    if (typeof window !== 'undefined') {
      localStorage.setItem(`session_${sessionId}`, JSON.stringify(sessionMetadata));
    }

    return sessionMetadata;
  }

  /**
   * Validate session with comprehensive security checks
   */
  static async validateSession(
    sessionId: string,
    request?: Request,
    config: Partial<SessionConfig> = {}
  ): Promise<{ isValid: boolean; metadata?: SessionMetadata; reason?: string }> {
    const sessionConfig = { ...this.DEFAULT_CONFIG, ...config };
    
    try {
      // Get session metadata
      let metadata = this.sessions.get(sessionId);
      
      // Try to load from persistent storage if not in memory
      if (!metadata && typeof window !== 'undefined') {
        const stored = localStorage.getItem(`session_${sessionId}`);
        if (stored) {
          metadata = JSON.parse(stored);
          if (metadata) {
            this.sessions.set(sessionId, metadata);
          }
        }
      }

      if (!metadata) {
        return { isValid: false, reason: 'Session not found' };
      }

      const now = Date.now();

      // Check if session is active
      if (!metadata.isActive) {
        return { isValid: false, reason: 'Session inactive' };
      }

      // Check session age
      if (now - metadata.createdAt > sessionConfig.maxAge) {
        await this.terminateSession(sessionId, 'expired');
        return { isValid: false, reason: 'Session expired' };
      }

      // Check inactivity timeout
      if (now - metadata.lastActivity > sessionConfig.inactivityTimeout) {
        await this.terminateSession(sessionId, 'timeout');
        return { isValid: false, reason: 'Session timed out' };
      }

      // Security checks
      if (sessionConfig.securityChecks && request) {
        const securityCheck = await this.performSecurityChecks(metadata, request);
        if (!securityCheck.passed) {
          await this.flagSession(sessionId, securityCheck.flags);
          
          if (securityCheck.severity === 'critical') {
            await this.terminateSession(sessionId, 'security_violation');
            return { isValid: false, reason: 'Security violation detected' };
          }
        }
      }

      // Update last activity
      metadata.lastActivity = now;
      this.sessions.set(sessionId, metadata);

      // Update persistent storage
      if (typeof window !== 'undefined') {
        localStorage.setItem(`session_${sessionId}`, JSON.stringify(metadata));
      }

      return { isValid: true, metadata };

    } catch (error) {
      this.errorTracker.captureException(
        error instanceof Error ? error : new Error(String(error)),
        {
          tags: { context: 'session_validation', sessionId },
          extra: { sessionId, userId: this.sessions.get(sessionId)?.userId }
        }
      );

      return { isValid: false, reason: 'Validation error' };
    }
  }

  /**
   * Terminate session with cleanup
   */
  static async terminateSession(
    sessionId: string,
    reason: 'logout' | 'timeout' | 'expired' | 'security_violation' | 'concurrent_limit'
  ): Promise<void> {
    const metadata = this.sessions.get(sessionId);
    
    if (metadata) {
      // Mark as inactive
      metadata.isActive = false;
      this.sessions.set(sessionId, metadata);

      // Log security event
      await this.logSecurityEvent({
        type: reason === 'logout' ? 'logout' : 'timeout',
        sessionId,
        userId: metadata.userId,
        timestamp: Date.now(),
        details: { reason },
        severity: reason === 'security_violation' ? 'critical' : 'info'
      });

      // Clean up persistent storage
      if (typeof window !== 'undefined') {
        localStorage.removeItem(`session_${sessionId}`);
        
        // Clear related auth data
        localStorage.removeItem('auth_last_activity');
        localStorage.removeItem('auth_session_id');
      }

      // Remove from memory after delay to allow for cleanup
      setTimeout(() => {
        this.sessions.delete(sessionId);
      }, 5000);
    }
  }

  /**
   * Get all active sessions for a user
   */
  static getActiveSessions(userId: string): SessionMetadata[] {
    return Array.from(this.sessions.values())
      .filter(session => session.userId === userId && session.isActive);
  }

  /**
   * Terminate all sessions for a user
   */
  static async terminateAllUserSessions(
    userId: string,
    excludeSessionId?: string
  ): Promise<void> {
    const userSessions = this.getActiveSessions(userId);
    
    for (const session of userSessions) {
      if (session.sessionId !== excludeSessionId) {
        await this.terminateSession(session.sessionId, 'concurrent_limit');
      }
    }
  }

  /**
   * Generate secure session ID
   */
  private static generateSecureSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomBytes = crypto.getRandomValues(new Uint8Array(32));
    const randomString = Array.from(randomBytes, byte => 
      byte.toString(16).padStart(2, '0')
    ).join('');
    return `${timestamp}_${randomString}`;
  }

  /**
   * Extract IP address from request
   */
  private static extractIpAddress(request?: Request): string | undefined {
    if (!request) return undefined;

    // Check various headers for IP address
    const headers = [
      'x-forwarded-for',
      'x-real-ip',
      'x-client-ip',
      'cf-connecting-ip'
    ];

    for (const header of headers) {
      const value = request.headers.get(header);
      if (value) {
        // Take the first IP if there are multiple
        return value.split(',')[0].trim();
      }
    }

    return undefined;
  }

  /**
   * Generate device fingerprint
   */
  private static async generateDeviceFingerprint(request?: Request): Promise<string | undefined> {
    if (!request) return undefined;

    const userAgent = request.headers.get('user-agent') || '';
    const acceptLanguage = request.headers.get('accept-language') || '';
    const acceptEncoding = request.headers.get('accept-encoding') || '';

    // Create a simple fingerprint from available headers
    const fingerprint = `${userAgent}|${acceptLanguage}|${acceptEncoding}`;
    
    // Hash the fingerprint for privacy
    const encoder = new TextEncoder();
    const data = encoder.encode(fingerprint);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Calculate security level based on request characteristics
   */
  private static calculateSecurityLevel(request?: Request): 'low' | 'medium' | 'high' {
    if (!request) return 'low';

    let score = 0;

    // Check for HTTPS
    if (request.url.startsWith('https://')) score += 2;

    // Check for security headers
    if (request.headers.get('x-forwarded-proto') === 'https') score += 1;
    if (request.headers.get('sec-fetch-site')) score += 1;
    if (request.headers.get('sec-fetch-mode')) score += 1;

    // Check user agent
    const userAgent = request.headers.get('user-agent') || '';
    if (userAgent.includes('Chrome') || userAgent.includes('Firefox') || userAgent.includes('Safari')) {
      score += 1;
    }

    if (score >= 4) return 'high';
    if (score >= 2) return 'medium';
    return 'low';
  }

  /**
   * Perform security checks on session
   */
  private static async performSecurityChecks(
    metadata: SessionMetadata,
    request: Request
  ): Promise<{ passed: boolean; flags: string[]; severity: 'info' | 'warning' | 'critical' }> {
    const flags: string[] = [];
    let severity: 'info' | 'warning' | 'critical' = 'info';

    // IP address validation
    const currentIp = this.extractIpAddress(request);
    if (metadata.ipAddress && currentIp && metadata.ipAddress !== currentIp) {
      flags.push('ip_change');
      severity = 'warning';
    }

    // User agent validation
    const currentUserAgent = request.headers.get('user-agent');
    if (metadata.userAgent && currentUserAgent && metadata.userAgent !== currentUserAgent) {
      flags.push('user_agent_change');
      severity = 'warning';
    }

    // Device fingerprint validation
    const currentFingerprint = await this.generateDeviceFingerprint(request);
    if (metadata.deviceFingerprint && currentFingerprint && 
        metadata.deviceFingerprint !== currentFingerprint) {
      flags.push('device_change');
      severity = 'critical';
    }

    // Suspicious activity detection
    const activityCheck = this.detectSuspiciousActivity(metadata);
    if (activityCheck.suspicious) {
      flags.push(...activityCheck.flags);
      severity = 'critical';
    }

    return {
      passed: severity !== 'critical',
      flags,
      severity
    };
  }

  /**
   * Detect suspicious activity patterns
   */
  private static detectSuspiciousActivity(metadata: SessionMetadata): { suspicious: boolean; flags: string[] } {
    const flags: string[] = [];
    const now = Date.now();

    // Check for rapid activity changes
    if (now - metadata.lastActivity < 100) { // Less than 100ms since last activity
      flags.push('rapid_activity');
    }

    // Check for existing security flags
    if (metadata.flags.includes('multiple_violations')) {
      flags.push('repeat_offender');
    }

    return {
      suspicious: flags.length > 0,
      flags
    };
  }

  /**
   * Flag session for security review
   */
  private static async flagSession(sessionId: string, flags: string[]): Promise<void> {
    const metadata = this.sessions.get(sessionId);
    if (metadata) {
      metadata.flags.push(...flags);
      this.sessions.set(sessionId, metadata);

      // Log security event
      await this.logSecurityEvent({
        type: 'suspicious_activity',
        sessionId,
        userId: metadata.userId,
        timestamp: Date.now(),
        details: { flags },
        severity: 'warning'
      });
    }
  }

  /**
   * Enforce concurrent session limit
   */
  private static async enforceConcurrentSessionLimit(
    userId: string,
    maxSessions: number
  ): Promise<void> {
    const activeSessions = this.getActiveSessions(userId);
    
    if (activeSessions.length >= maxSessions) {
      // Terminate oldest sessions
      const sessionsToTerminate = activeSessions
        .sort((a, b) => a.lastActivity - b.lastActivity)
        .slice(0, activeSessions.length - maxSessions + 1);

      for (const session of sessionsToTerminate) {
        await this.terminateSession(session.sessionId, 'concurrent_limit');
      }
    }
  }

  /**
   * Log security event
   */
  private static async logSecurityEvent(event: SessionSecurityEvent): Promise<void> {
    this.securityEvents.push(event);

    // Keep only recent events (last 1000)
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }

    // Log to error tracker for monitoring
    this.errorTracker.captureMessage(
      `Session security event: ${event.type}`,
      {
        tags: { 
          sessionEvent: event.type,
          severity: event.severity,
          userId: event.userId
        },
        extra: event.details
      }
    );

    // Log critical events immediately
    if (event.severity === 'critical') {
      console.error('Critical session security event:', event);
    }
  }

  /**
   * Get security events for analysis
   */
  static getSecurityEvents(
    userId?: string,
    since?: number
  ): SessionSecurityEvent[] {
    let events = this.securityEvents;

    if (userId) {
      events = events.filter(event => event.userId === userId);
    }

    if (since) {
      events = events.filter(event => event.timestamp >= since);
    }

    return events.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Clean up expired sessions
   */
  static cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];

    for (const [sessionId, metadata] of this.sessions.entries()) {
      if (!metadata.isActive || 
          now - metadata.createdAt > this.DEFAULT_CONFIG.maxAge ||
          now - metadata.lastActivity > this.DEFAULT_CONFIG.inactivityTimeout) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      this.terminateSession(sessionId, 'expired');
    }
  }
}

// Initialize cleanup interval
if (typeof window !== 'undefined') {
  setInterval(() => {
    EnhancedSessionManager.cleanupExpiredSessions();
  }, 5 * 60 * 1000); // Clean up every 5 minutes
}
