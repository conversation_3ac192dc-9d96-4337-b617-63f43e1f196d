/**
 * Server-side authentication state utilities
 * This file should only be imported on the server side
 */

import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { User } from '@prisma/client';
import { AuthState } from './auth-state-manager';

/**
 * Server-side authentication state validation
 * Note: This function should only be used on the server side
 */
export async function getServerAuthState(): Promise<AuthState> {
  // This function should only be called on the server
  if (typeof window !== 'undefined') {
    throw new Error('getServerAuthState should only be called on the server side');
  }

  try {
    const session = await getServerSession(authOptions);
    
    if (session?.user) {
      // Check admin status on server
      const isAdmin = await checkServerAdminStatus(session.user.id);
      
      return {
        isAuthenticated: true,
        isLoading: false,
        user: session.user as User,
        sessionId: (session as any).sessionId || null,
        lastActivity: Date.now(),
        isAdmin,
        error: null
      };
    }

    return {
      isAuthenticated: false,
      isLoading: false,
      user: null,
      sessionId: null,
      lastActivity: null,
      isAdmin: false,
      error: null
    };
  } catch (error) {
    console.error('Server auth state error:', error);
    return {
      isAuthenticated: false,
      isLoading: false,
      user: null,
      sessionId: null,
      lastActivity: null,
      isAdmin: false,
      error: error instanceof Error ? error.message : 'Server authentication error'
    };
  }
}

/**
 * Check admin status on server
 */
async function checkServerAdminStatus(userId: string): Promise<boolean> {
  try {
    // Import here to avoid circular dependencies
    const { isUserAdmin } = await import('@/lib/auth-utils');
    return await isUserAdmin(userId);
  } catch (error) {
    console.error('Error checking server admin status:', error);
    return false;
  }
}
