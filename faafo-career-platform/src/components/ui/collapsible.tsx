/**
 * Collapsible Component
 * A simple collapsible/expandable content component
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight } from 'lucide-react';

export interface CollapsibleProps {
  children: React.ReactNode;
  trigger: React.ReactNode;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  className?: string;
  triggerClassName?: string;
  contentClassName?: string;
  disabled?: boolean;
  animated?: boolean;
}

export function Collapsible({
  children,
  trigger,
  defaultOpen = false,
  open: controlledOpen,
  onOpenChange,
  className,
  triggerClassName,
  contentClassName,
  disabled = false,
  animated = true
}: CollapsibleProps) {
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<number | undefined>(undefined);

  // Use controlled or internal state
  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;

  // Handle open state changes
  const handleToggle = () => {
    if (disabled) return;
    
    const newOpen = !isOpen;
    
    if (controlledOpen === undefined) {
      setInternalOpen(newOpen);
    }
    
    onOpenChange?.(newOpen);
  };

  // Calculate height for animation
  useEffect(() => {
    if (!animated || !contentRef.current) return;

    if (isOpen) {
      const scrollHeight = contentRef.current.scrollHeight;
      setHeight(scrollHeight);
    } else {
      setHeight(0);
    }
  }, [isOpen, animated, children]);

  return (
    <div className={cn('collapsible', className)}>
      {/* Trigger */}
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={cn(
          'flex items-center justify-between w-full text-left',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          triggerClassName
        )}
        aria-expanded={isOpen}
        aria-controls="collapsible-content"
      >
        <span className="flex-1">{trigger}</span>
        <span className="ml-2 flex-shrink-0">
          {isOpen ? (
            <ChevronDown className="h-4 w-4 transition-transform duration-200" />
          ) : (
            <ChevronRight className="h-4 w-4 transition-transform duration-200" />
          )}
        </span>
      </button>

      {/* Content */}
      <div
        ref={contentRef}
        id="collapsible-content"
        className={cn(
          'overflow-hidden',
          animated && 'transition-all duration-300 ease-in-out',
          contentClassName
        )}
        style={
          animated
            ? {
                height: height !== undefined ? `${height}px` : undefined,
                opacity: isOpen ? 1 : 0
              }
            : undefined
        }
        aria-hidden={!isOpen}
      >
        <div className={cn('pt-2', !isOpen && !animated && 'hidden')}>
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * Collapsible Trigger Component
 */
export interface CollapsibleTriggerProps {
  children: React.ReactNode;
  className?: string;
  asChild?: boolean;
}

export function CollapsibleTrigger({
  children,
  className,
  asChild = false
}: CollapsibleTriggerProps) {
  if (asChild && React.isValidElement(children)) {
    const childProps = children.props as any;
    const mergedClassName = cn(className, childProps?.className);
    return React.cloneElement(children, {
      ...childProps,
      className: mergedClassName
    } as any);
  }

  return (
    <div className={className}>
      {children}
    </div>
  );
}

/**
 * Collapsible Content Component
 */
export interface CollapsibleContentProps {
  children: React.ReactNode;
  className?: string;
}

export function CollapsibleContent({
  children,
  className
}: CollapsibleContentProps) {
  return (
    <div className={className}>
      {children}
    </div>
  );
}

/**
 * Simple Accordion Component using Collapsible
 */
export interface AccordionItem {
  id: string;
  trigger: React.ReactNode;
  content: React.ReactNode;
  disabled?: boolean;
}

export interface AccordionProps {
  items: AccordionItem[];
  type?: 'single' | 'multiple';
  defaultValue?: string | string[];
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  className?: string;
  itemClassName?: string;
  triggerClassName?: string;
  contentClassName?: string;
}

export function Accordion({
  items,
  type = 'single',
  defaultValue,
  value: controlledValue,
  onValueChange,
  className,
  itemClassName,
  triggerClassName,
  contentClassName
}: AccordionProps) {
  const [internalValue, setInternalValue] = useState<string | string[]>(
    defaultValue || (type === 'single' ? '' : [])
  );

  const value = controlledValue !== undefined ? controlledValue : internalValue;

  const handleValueChange = (itemId: string) => {
    let newValue: string | string[];

    if (type === 'single') {
      newValue = value === itemId ? '' : itemId;
    } else {
      const currentArray = Array.isArray(value) ? value : [];
      newValue = currentArray.includes(itemId)
        ? currentArray.filter(id => id !== itemId)
        : [...currentArray, itemId];
    }

    if (controlledValue === undefined) {
      setInternalValue(newValue);
    }

    onValueChange?.(newValue);
  };

  const isItemOpen = (itemId: string) => {
    if (type === 'single') {
      return value === itemId;
    } else {
      return Array.isArray(value) && value.includes(itemId);
    }
  };

  return (
    <div className={cn('accordion space-y-2', className)}>
      {items.map((item) => (
        <div key={item.id} className={cn('accordion-item', itemClassName)}>
          <Collapsible
            open={isItemOpen(item.id)}
            onOpenChange={() => handleValueChange(item.id)}
            trigger={item.trigger}
            disabled={item.disabled}
            triggerClassName={triggerClassName}
            contentClassName={contentClassName}
          >
            {item.content}
          </Collapsible>
        </div>
      ))}
    </div>
  );
}

// Export default
export default Collapsible;
