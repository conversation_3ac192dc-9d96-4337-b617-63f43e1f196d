// Force dynamic rendering for error pages
export const dynamic = 'force-dynamic';

// Disable static generation completely
export const generateStaticParams = () => [];

export default function Custom500() {
  // Return the simplest possible React element to avoid any rendering issues
  return (
    <html lang="en">
      <head>
        <title>500 - Server Error</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <style dangerouslySetInnerHTML={{
          __html: `
            body {
              margin: 0;
              padding: 0;
              font-family: system-ui, sans-serif;
              background-color: #f9fafb;
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .container {
              max-width: 28rem;
              width: 100%;
              background-color: white;
              border-radius: 0.5rem;
              box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
              padding: 2rem;
              text-align: center;
            }
            .error-code {
              font-size: 3.75rem;
              font-weight: bold;
              color: #ef4444;
              margin-bottom: 1rem;
            }
            .error-title {
              font-size: 1.5rem;
              font-weight: 600;
              color: #111827;
              margin-bottom: 1rem;
            }
            .error-message {
              color: #6b7280;
              margin-bottom: 1.5rem;
            }
            .home-link {
              display: inline-block;
              background-color: #2563eb;
              color: white;
              padding: 0.75rem 1.5rem;
              border-radius: 0.375rem;
              text-decoration: none;
              font-weight: 500;
            }
          `
        }} />
      </head>
      <body>
        <div className="container">
          <div className="error-code">500</div>
          <h1 className="error-title">Server Error</h1>
          <p className="error-message">We apologize for the inconvenience. A server error occurred.</p>
          <a href="/" className="home-link">Go Home</a>
        </div>
      </body>
    </html>
  );
}
