'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, FileText, Edit, Trash2, Download, Eye, Copy } from 'lucide-react';
import { ResumeBuilder, Resume } from '@/components/resume-builder';
import { PageLayout } from '@/components/layout/PageLayout';
import { AuthGuard } from '@/components/auth/AuthGuard';

interface ResumeListItem {
  id: string;
  title: string;
  template: string;
  isPublic: boolean;
  lastExported?: string;
  exportCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function ResumeBuilderPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [resumes, setResumes] = useState<ResumeListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<'list' | 'builder'>('list');
  const [editingResumeId, setEditingResumeId] = useState<string | null>(null);

  // Check if we should open builder directly
  useEffect(() => {
    const action = searchParams?.get('action');
    const resumeId = searchParams?.get('id');

    if (action === 'new') {
      setCurrentView('builder');
      setEditingResumeId(null);
    } else if ((action === 'edit' || action === 'preview') && resumeId) {
      setCurrentView('builder');
      setEditingResumeId(resumeId);
    }
  }, [searchParams]);

  // Load resumes when component mounts
  useEffect(() => {
    if (status === 'authenticated') {
      loadResumes();
    }
  }, [status]);

  const loadResumes = async () => {
    setLoading(true);
    setError(null);

    // Set empty array immediately to show UI faster
    setResumes([]);

    try {
      const response = await fetch('/api/resume-builder', {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });

      if (!response.ok) {
        throw new Error('Failed to load resumes');
      }

      const data = await response.json();
      if (data.success) {
        setResumes(data.data);
      } else {
        throw new Error(data.error || 'Failed to load resumes');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load resumes');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNew = () => {
    setCurrentView('builder');
    setEditingResumeId(null);
    router.push('/resume-builder?action=new');
  };

  const handleEditResume = (resumeId: string) => {
    setCurrentView('builder');
    setEditingResumeId(resumeId);
    router.push(`/resume-builder?action=edit&id=${resumeId}`);
  };

  const handleDeleteResume = async (resumeId: string) => {
    if (!confirm('Are you sure you want to delete this resume? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/resume-builder/${resumeId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete resume');
      }

      const data = await response.json();
      if (data.success) {
        // Remove from local state
        setResumes(prev => prev.filter(resume => resume.id !== resumeId));
      } else {
        throw new Error(data.error || 'Failed to delete resume');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete resume');
    }
  };

  const handlePreviewResume = (resumeId: string) => {
    // Open preview in builder mode
    setCurrentView('builder');
    setEditingResumeId(resumeId);
    router.push(`/resume-builder?action=preview&id=${resumeId}`);
  };

  const handleDownloadResume = async (resumeId: string) => {
    try {
      // For now, show a message that download is coming soon
      alert('Download functionality is coming soon! You can preview and copy your resume content for now.');

      // TODO: Implement actual download functionality
      // This would typically involve:
      // 1. Fetching the resume data
      // 2. Generating a PDF or other format
      // 3. Triggering the download

      // Example future implementation:
      // const response = await fetch(`/api/resume-builder/${resumeId}/export?format=pdf`);
      // const blob = await response.blob();
      // const url = window.URL.createObjectURL(blob);
      // const a = document.createElement('a');
      // a.href = url;
      // a.download = `resume-${resumeId}.pdf`;
      // a.click();
      // window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download resume');
    }
  };

  const handleResumeBuilderSave = (resume: Resume) => {
    // Update local state
    if (editingResumeId) {
      setResumes(prev => prev.map(r => 
        r.id === resume.id ? {
          ...r,
          title: resume.title,
          template: resume.template,
          isPublic: resume.isPublic,
          updatedAt: resume.updatedAt || new Date().toISOString()
        } : r
      ));
    } else {
      // Add new resume to list
      const newResumeItem: ResumeListItem = {
        id: resume.id!,
        title: resume.title,
        template: resume.template,
        isPublic: resume.isPublic,
        exportCount: 0,
        createdAt: resume.createdAt || new Date().toISOString(),
        updatedAt: resume.updatedAt || new Date().toISOString()
      };
      setResumes(prev => [newResumeItem, ...prev]);
    }

    // Return to list view
    setCurrentView('list');
    setEditingResumeId(null);
    router.push('/resume-builder');
  };

  const handleResumeBuilderCancel = () => {
    setCurrentView('list');
    setEditingResumeId(null);
    router.push('/resume-builder');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const ResumeBuilderContent = () => {
    if (currentView === 'builder') {
    const action = searchParams?.get('action');
    return (
      <PageLayout>
        <ResumeBuilder
          resumeId={editingResumeId || undefined}
          initialMode={action === 'preview' ? 'preview' : 'edit'}
          onSave={handleResumeBuilderSave}
          onCancel={handleResumeBuilderCancel}
        />
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="max-w-6xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">My Resumes</h1>
            <p className="text-muted-foreground">Create and manage your professional resumes</p>
          </div>
          <Button onClick={handleCreateNew} size="lg">
            <Plus className="w-4 h-4 mr-2" />
            Create New Resume
          </Button>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Resume List */}
        {loading ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        ) : resumes.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <FileText className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold mb-2">No resumes yet</h3>
              <p className="text-muted-foreground mb-6">
                Create your first professional resume to get started
              </p>
              <Button onClick={handleCreateNew} size="lg">
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Resume
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {resumes.map((resume) => (
              <Card key={resume.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-2">{resume.title}</CardTitle>
                      <CardDescription>
                        Template: {resume.template} • Updated {formatDate(resume.updatedAt)}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      {resume.isPublic && (
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                          Public
                        </span>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      <p>Created: {formatDate(resume.createdAt)}</p>
                      <p>Exports: {resume.exportCount}</p>
                      {resume.lastExported && (
                        <p>Last exported: {formatDate(resume.lastExported)}</p>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditResume(resume.id)}
                        className="flex-1"
                      >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePreviewResume(resume.id)}
                        title="Preview Resume"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadResume(resume.id)}
                        title="Download Resume"
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteResume(resume.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Quick Stats */}
        {resumes.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Resume Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">{resumes.length}</div>
                  <div className="text-sm text-muted-foreground">Total Resumes</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {resumes.filter(r => r.isPublic).length}
                  </div>
                  <div className="text-sm text-muted-foreground">Public Resumes</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {resumes.reduce((sum, r) => sum + r.exportCount, 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Exports</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {new Set(resumes.map(r => r.template)).size}
                  </div>
                  <div className="text-sm text-muted-foreground">Templates Used</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </PageLayout>
    );
  };

  return (
    <AuthGuard requireAuth={true} redirectTo="/login">
      <ResumeBuilderContent />
    </AuthGuard>
  );
}
