// Force dynamic rendering for error pages
export const dynamic = 'force-dynamic';

export default function NotFound() {
  return (
    <html lang="en">
      <head>
        <title>404 - Page Not Found</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <style dangerouslySetInnerHTML={{
          __html: `
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
              font-family: system-ui, -apple-system, sans-serif; 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh; 
              display: flex; 
              align-items: center; 
              justify-content: center; 
              color: #333;
            }
            .container { 
              max-width: 500px; 
              width: 90%; 
              background: white; 
              border-radius: 16px; 
              box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
              padding: 3rem 2rem; 
              text-align: center; 
            }
            .error-code { 
              font-size: 6rem; 
              font-weight: 900; 
              color: #6b7280; 
              margin-bottom: 1rem; 
              line-height: 1;
            }
            .error-title { 
              font-size: 2rem; 
              font-weight: 700; 
              color: #1f2937; 
              margin-bottom: 1rem; 
            }
            .error-message { 
              color: #6b7280; 
              margin-bottom: 2rem; 
              line-height: 1.6;
            }
            .btn { 
              display: inline-block;
              padding: 0.75rem 2rem; 
              background: #2563eb; 
              color: white; 
              border-radius: 8px; 
              text-decoration: none; 
              font-weight: 600; 
              transition: background 0.2s;
            }
            .btn:hover { 
              background: #1d4ed8; 
            }
          `
        }} />
      </head>
      <body>
        <div className="container">
          <div className="error-code">404</div>
          <h1 className="error-title">Page Not Found</h1>
          <p className="error-message">
            The page you are looking for does not exist or has been moved.
          </p>
          <a href="/" className="btn">
            Go Home
          </a>
        </div>
      </body>
    </html>
  );
}
